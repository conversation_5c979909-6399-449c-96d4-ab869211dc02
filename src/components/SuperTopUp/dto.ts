import { SuperTopUpApiResponse } from "./types";
import { featuresComparisonData } from "@/components/Compare/data/featuresComparisonData";


const transformSuperTopUpData = (pageData: SuperTopUpApiResponse) => {
  if (!pageData) {
    return null;
  }

  const aboutContent = pageData.super_top_up_page_sections.find(
    (section) => section.section === "about"
  );
  const benefitsContent = pageData.super_top_up_page_sections.find(
    (section) => section.section === "benefits"
  );
  const highlightsContent = pageData.super_top_up_page_sections.find(
    (section) => section.section === "highlights"
  );
  const uniqueFeaturesContent = pageData.super_top_up_page_sections.find(
    (section) => section.section === "unique_features"
  );
  const eligiblityCriteriaContent = pageData.super_top_up_page_sections.find(
    (section) => section.section === "eligiblity_criteria"
  );
  const factorsToConsiderContent = pageData.super_top_up_page_sections.find(
    (section) => section.section === "factors_to_consider"
  );

  const heroSection = {
    pill: pageData.pill_content,
    title: pageData.hero_title,
    description: pageData.hero_description,
    image: pageData.hero_image_url,
    breadcrumbPath: [
      { name: "OneAssure", url: `${process.env.NEXT_PUBLIC_BASE_URL}/` },
      {
        name: "Health Insurance",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/health-insurance/`,
      },
      {
        name: pageData.health_insurer.name,
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${pageData.health_insurer.slug}-health-insurance/hi/${pageData.health_insurer.id}`,
      },
      {
        name: "Top Up Plan",
        url: `${process.env.NEXT_PUBLIC_BASE_URL}/${pageData.slug}/st/${pageData.id}`,
      },
    ],
    stats: pageData.super_top_up_page_hero_cards.map((card) => {
      return {
        id: card.id,
        title: card.title,
        value: card.description,
        suffix: "",
        prefix: "",
      };
    }),
  };

  const verdictSection = {
    pill: pageData.super_top_up_page_verdicts.pill_content,
    title: pageData.super_top_up_page_verdicts.title,
    whatWeLike: {
      heading: "Pros",
      points:
        pageData.super_top_up_page_verdicts.super_top_up_page_verdict_pros_cons
          .filter((point) => point.type === "pro")
          .map((point) => point.points) || [],
    },
    AreasOfImprovement: {
      heading: "Cons",
      points:
        pageData.super_top_up_page_verdicts.super_top_up_page_verdict_pros_cons
          .filter((point) => point.type === "con")
          .map((point) => point.points) || [],
    },
    verdict: pageData.super_top_up_page_verdicts.verdict,
  };

  const aboutSection = {
    pill: aboutContent?.pill_content || "",
    title: aboutContent?.section_title || "",
    description: aboutContent?.section_description || "",
    cards:
      aboutContent?.super_top_up_page_section_cards.map((card) => {
        return {
          id: card.id,
          title: card.title,
          icon_url: card.icon_url,
          description: card.description,
        };
      }) || [],
  };

  const benefitsSection = {
    pill: benefitsContent?.pill_content || "",
    title: benefitsContent?.section_title || "",
    description: benefitsContent?.section_description || "",
    cards:
      benefitsContent?.super_top_up_page_section_cards.map((card) => {
        return {
          id: card.id,
          title: card.title,
          icon_url: card.icon_url,
          description: card.description,
        };
      }) || [],
  };

  const highlightsSection = {
    pill: highlightsContent?.pill_content || "",
    title: highlightsContent?.section_title || "",
    description: highlightsContent?.section_description || "",
    cards:
      highlightsContent?.super_top_up_page_section_cards.map((card) => {
        return {
          id: card.id,
          title: card.title,
          icon_url: card.icon_url,
          description: card.description,
        };
      }) || [],
  };

  const uniqueFeaturesSection = {
    pill: uniqueFeaturesContent?.pill_content || "",
    title: uniqueFeaturesContent?.section_title || "",
    description: uniqueFeaturesContent?.section_description || "",
    cards:
      uniqueFeaturesContent?.super_top_up_page_section_cards.map((card) => {
        return {
          id: card.id,
          title: card.title,
          icon_url: card.icon_url,
          description: card.description,
        };
      }) || [],
  };

  const eligiblityCriteriaSection = {
    pill: eligiblityCriteriaContent?.pill_content || "",
    title: eligiblityCriteriaContent?.section_title || "",
    description: eligiblityCriteriaContent?.section_description || "",
    cards:
      eligiblityCriteriaContent?.super_top_up_page_section_cards.map((card) => {
        return {
          id: card.id,
          title: card.title,
          icon_url: card.icon_url,
          description: card.description,
        };
      }) || [],
  };

  const factorsToConsiderSection = {
    pill: factorsToConsiderContent?.pill_content || "",
    title: factorsToConsiderContent?.section_title || "",
    description: factorsToConsiderContent?.section_description || "",
    cards:
      factorsToConsiderContent?.super_top_up_page_section_cards.map((card) => {
        return {
          id: card.id,
          title: card.title,
          icon_url: card.icon_url,
          description: card.description,
        };
      }) || [],
  };

  const pageNavigationSection = {
    activeTab: "expert-review",
    tabs: [
      { label: "Verdict", id: "expert-review" },
      { label: "About Super Top Up", id: "about-super-top-up" },
      { label: "Testimonials", id: "testimonials" },
      { label: "Unique Features", id: "unique-features" },
      { label: "Factors To Consider", id: "factors-to-consider" },
      { label: "Inclusions", id: "inclusions-and-exclusions" },
      { label: "Claim Settlement Process", id: "claim-settlement" },
      { label: "Renewal Process", id: "renewal-process" },
      { label: "FAQs", id: "faqs" },
    ],
  };

  const inclusionPoint =
    pageData.super_top_up_page_inclusion_section.super_top_up_page_inclusion_section_points.find(
      (point) => point.type === "pro"
    );
  const exclusionPoint =
    pageData.super_top_up_page_inclusion_section.super_top_up_page_inclusion_section_points.find(
      (point) => point.type === "con"
    );

  const inclusionSection = {
    pill: pageData.super_top_up_page_inclusion_section.pill_content,
    title: pageData.super_top_up_page_inclusion_section.section_title,
    description:
      pageData.super_top_up_page_inclusion_section.section_description,
    inclusions: {
      title: "Inclusions",
      points: inclusionPoint?.points || [],
    },
    exclusions: {
      title: "Exclusions",
      points: exclusionPoint?.points || [],
    },
  };

  const insuranceCategorySection = {
    pill: pageData.super_top_up_page_insurance_category_section.pill_content || "Insurance Company",
    heading: "Related Health Insurance Plans",
     featuresComparisonData: featuresComparisonData.plans,
  };

  const renewalSection = {
    pill: pageData.super_top_up_page_renewal_section.pill_content,
    title: pageData.super_top_up_page_renewal_section.section_title,
    description: pageData.super_top_up_page_renewal_section.section_description,
    renewalSteps:
      pageData.super_top_up_page_renewal_section.super_top_up_page_renewal_types.map(
        (type) => {
          return {
            id: type.id,
            title: type.title,
            type: type.type,
            types: type.super_top_up_page_renewal_steps.map((step) => {
              return {
                id: step.id,
                title: step.title,
                description: step.description,
              };
            }),
          };
        }
      ),
  };

  const howToBuySection = {
    pill: pageData.super_top_up_page_how_to_buy_section.pill_content,
    title: pageData.super_top_up_page_how_to_buy_section.section_title,
    description:
      pageData.super_top_up_page_how_to_buy_section.section_description,
    steps:
      pageData.super_top_up_page_how_to_buy_section.super_top_up_page_how_to_buy_steps.map(
        (step) => {
          return {
            id: step.id,
            question: step.title,
            answer: step.description,
          };
        }
      ),
  };

  const faqSection = {
    pill: pageData.super_top_up_page_faq_section.pill_content,
    title: pageData.super_top_up_page_faq_section.section_title,
    description: pageData.super_top_up_page_faq_section.section_description,
    faqs: pageData.super_top_up_page_faq_section.super_top_up_page_faq_section_points.map(
      (faq) => {
        return {
          id: faq.id,
          question: faq.question,
          answer: faq.answer,
        };
      }
    ),
  };

  const claimSettlementSection = {
    pill: pageData.super_top_up_page_claim_settlement_section.pill_content,
    title: pageData.super_top_up_page_claim_settlement_section.section_title,
    description:
      pageData.super_top_up_page_claim_settlement_section.section_description,
    settlements:
      pageData.super_top_up_page_claim_settlement_section.super_top_up_page_claim_settlement_types.map(
        (type) => {
          return {
            id: type.id,
            title: type.title,
            types: type.super_top_up_page_claim_settlement_steps.map((step) => {
              return {
                id: step.id,
                title: step.title,
                description: step.description,
              };
            }),
          };
        }
      ),
  };

  const testimonialSection = {
    pill: pageData.super_top_up_page_testimonial_section.pill_content,
    testimonials:
      pageData.super_top_up_page_testimonial_section.super_top_up_page_testimonial_section_points.map(
        (point) => {
          return {
            id: point.id,
            name: point.name,
            content: point.description,
          };
        }
      ),
    sectionHeaderProps: {
      id: "testimonials",
      pill: pageData.super_top_up_page_testimonial_section.pill_content,
      heading: pageData.super_top_up_page_testimonial_section.section_title,
      subheading:
        pageData.super_top_up_page_testimonial_section.section_description,
    },
  };

  const healthInsurer = {
    id: pageData.health_insurer.id,
    name: pageData.health_insurer.name,
    slug: pageData.health_insurer.slug,
  };

  return {
    id: pageData.id,
    healthInsurer,
    heroSection,
    verdictSection,
    aboutSection,
    benefitsSection,
    highlightsSection,
    uniqueFeaturesSection,
    eligiblityCriteriaSection,
    factorsToConsiderSection,
    pageNavigationSection,
    inclusionSection,
    insuranceCategorySection,
    renewalSection,
    howToBuySection,
    faqSection,
    claimSettlementSection,
    testimonialSection,
  };
};

export type SuperTopUpData = ReturnType<typeof transformSuperTopUpData>;

export default transformSuperTopUpData;
